/* Debug CSS - Easy to toggle on/off for debugging purposes */

/* Debug borders and backgrounds */
.debug-mode {
    /* Add debug class to body or container to enable all debug styles */
}

.debug-mode * {
    /* Subtle debug borders on all elements */
    outline: 1px solid rgba(255, 0, 0, 0.1) !important;
}

.debug-mode .app-container {
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(255, 255, 0, 0.05) 10px,
        rgba(255, 255, 0, 0.05) 20px
    ) !important;
}

.debug-mode .sidebar {
    background: rgba(0, 255, 0, 0.05) !important;
    border: 2px solid lime !important;
}

.debug-mode .main-content {
    background: rgba(0, 0, 255, 0.05) !important;
    border: 2px solid blue !important;
}

/* Debug info overlays */
.debug-info {
    position: fixed;
    top: 50px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 12px;
    z-index: 1000;
    max-width: 300px;
}

.debug-info h4 {
    margin: 0 0 8px 0;
    color: #ffc107;
}

.debug-info ul {
    margin: 0;
    padding-left: 16px;
}

.debug-info li {
    margin: 2px 0;
}

/* Debug grid overlay */
.debug-grid {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 999;
    background-image: 
        linear-gradient(rgba(255, 0, 0, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 0, 0, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Debug element highlighting */
.debug-highlight {
    background: rgba(255, 255, 0, 0.3) !important;
    border: 2px solid orange !important;
    box-shadow: 0 0 10px rgba(255, 165, 0, 0.5) !important;
}

/* Debug console styles */
.debug-console {
    position: fixed;
    bottom: 10px;
    left: 10px;
    right: 10px;
    height: 200px;
    background: rgba(0, 0, 0, 0.9);
    color: #00ff00;
    font-family: monospace;
    font-size: 12px;
    padding: 10px;
    border-radius: 4px;
    overflow-y: auto;
    z-index: 1001;
    border: 1px solid #333;
}

.debug-console .log-entry {
    margin: 2px 0;
    padding: 2px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.debug-console .log-error {
    color: #ff6b6b;
}

.debug-console .log-warn {
    color: #feca57;
}

.debug-console .log-info {
    color: #48dbfb;
}

/* Debug performance indicators */
.debug-perf {
    position: fixed;
    top: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 11px;
    z-index: 1000;
}

.debug-perf .perf-good {
    color: #00ff00;
}

.debug-perf .perf-warn {
    color: #feca57;
}

.debug-perf .perf-bad {
    color: #ff6b6b;
}

/* Debug responsive breakpoints */
@media (max-width: 768px) {
    .debug-mode::before {
        content: "Mobile View";
        position: fixed;
        top: 0;
        left: 0;
        background: red;
        color: white;
        padding: 4px 8px;
        font-size: 12px;
        z-index: 1002;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .debug-mode::before {
        content: "Tablet View";
        position: fixed;
        top: 0;
        left: 0;
        background: orange;
        color: white;
        padding: 4px 8px;
        font-size: 12px;
        z-index: 1002;
    }
}

@media (min-width: 1025px) {
    .debug-mode::before {
        content: "Desktop View";
        position: fixed;
        top: 0;
        left: 0;
        background: green;
        color: white;
        padding: 4px 8px;
        font-size: 12px;
        z-index: 1002;
    }
}

/* Debug tool response styling */
.debug-mode .tool-response {
    border: 2px dashed purple !important;
}

.debug-mode .message-item {
    border-left: 3px solid rgba(0, 255, 255, 0.5) !important;
    padding-left: 8px !important;
}

/* Debug MCP status */
.debug-mode .mcp-status-bar {
    border: 2px solid cyan !important;
    position: relative;
}

.debug-mode .mcp-status-bar::after {
    content: "MCP DEBUG";
    position: absolute;
    top: -10px;
    right: 5px;
    background: cyan;
    color: black;
    padding: 2px 6px;
    font-size: 10px;
    border-radius: 2px;
}
