/* Enhanced Style CSS - Visual improvements and modern styling */

/* Enhanced animations and transitions */
* {
    transition: all 0.2s ease-in-out;
}

/* Enhanced button styles */
.btn {
    border-radius: 6px;
    font-weight: 500;
    letter-spacing: 0.025em;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Enhanced input styles */
input[type="text"], textarea {
    border-radius: 6px;
    border: 2px solid #e1e5e9;
    padding: 12px 16px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

input[type="text"]:focus, textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Enhanced chat interface */
.chat-pane {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

#chat-transcript {
    border-radius: 8px;
    border: 2px solid #f1f3f4;
    background: linear-gradient(to bottom, #ffffff, #fafbfc);
}

.message-item {
    padding: 12px 16px;
    margin: 8px 0;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.8);
    border-left: 4px solid transparent;
    transition: all 0.2s ease;
}

.message-item:hover {
    background: rgba(248, 249, 250, 0.9);
    border-left-color: #007bff;
    transform: translateX(2px);
}

.message-item .role {
    font-weight: 600;
    margin-right: 8px;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.message-item .role:contains("user") {
    background: #e3f2fd;
    color: #1976d2;
}

.message-item .role:contains("assistant") {
    background: #f3e5f5;
    color: #7b1fa2;
}

/* Enhanced tool response styling */
.response-box {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 16px;
    margin: 12px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;
}

.response-box::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #007bff, #6f42c1, #e83e8c);
    border-radius: 8px 8px 0 0;
}

/* Enhanced sidebar */
.sidebar {
    background: linear-gradient(180deg, #ffffff, #f8f9fa);
    border-right: 3px solid #e9ecef;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
}

.conv-item {
    border-radius: 8px;
    margin: 6px 0;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.conv-item:hover {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-color: #dee2e6;
    transform: translateX(4px);
}

.conv-item.active {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-color: #2196f3;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
}

/* Enhanced status indicators */
.mcp-status-bar {
    border-radius: 8px;
    border: 2px solid transparent;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.mcp-status-bar::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.mcp-status-bar:hover::before {
    left: 100%;
}

/* Enhanced debug toggle */
.debug-toggle {
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.debug-toggle:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* Enhanced loading states */
.fa-spin {
    animation: enhanced-spin 1s linear infinite;
}

@keyframes enhanced-spin {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.1); }
    100% { transform: rotate(360deg) scale(1); }
}

/* Enhanced scrollbars */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #c1c1c1, #a1a1a1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #a1a1a1, #818181);
}

/* Enhanced form styling */
.chat-input {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: 8px;
    border-top: 3px solid #e9ecef;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

/* Enhanced responsive design */
@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 3px solid #e9ecef;
    }
    
    .message-item {
        padding: 8px 12px;
        margin: 4px 0;
    }
}

/* Enhanced focus indicators */
*:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Enhanced typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    letter-spacing: -0.025em;
    color: #2c3e50;
}

/* Enhanced code blocks */
pre, code {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

/* Enhanced badges and labels */
.badge {
    border-radius: 12px;
    padding: 4px 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Enhanced hover effects */
.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Enhanced glass morphism effect */
.glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}
